#!/usr/bin/env python3
"""
Debug script to identify problematic imports in main.py
"""

import sys
import time

def test_import(module_name, import_statement):
    print(f"Testing import: {module_name}...")
    start_time = time.time()
    try:
        exec(import_statement)
        end_time = time.time()
        print(f"✓ {module_name} imported successfully in {end_time - start_time:.2f}s")
        return True
    except Exception as e:
        end_time = time.time()
        print(f"✗ {module_name} failed in {end_time - start_time:.2f}s: {e}")
        return False

# Test imports from main.py one by one
imports_to_test = [
    ("asyncio", "import asyncio"),
    ("openai", "from openai import AsyncOpenAI"),
    ("url_processor", "import url_processor"),
    ("storage", "import storage"),
    ("video_processing", "import video_processing"),
    ("models", "import models"),
    ("database", "from database import SessionLocal, engine, Base"),
    ("fastapi.staticfiles", "from fastapi.staticfiles import StaticFiles"),
    ("requests", "import requests"),
    ("google.cloud.texttospeech", "from google.cloud import texttospeech"),
    ("openai_direct", "import openai"),
    ("moviepy", "from moviepy.editor import VideoFileClip"),
    ("cloudinary", "import cloudinary.uploader; import cloudinary"),
    ("sqlalchemy", "from sqlalchemy.orm import Session"),
    ("pydantic", "from pydantic import BaseModel"),
    ("passlib", "from passlib.context import CryptContext"),
    ("jose", "from jose import JWTError, jwt"),
    ("fastapi.security", "from fastapi.security import OAuth2PasswordBearer, OAuth2PasswordRequestForm"),
    ("fastapi.middleware.cors", "from fastapi.middleware.cors import CORSMiddleware"),
    ("fastapi", "from fastapi import FastAPI, Depends, HTTPException, status, File, UploadFile, Form, BackgroundTasks"),
    ("advanced_video_processor", "from advanced_video_processor import AdvancedVideoProcessor, process_video_with_enhancements"),
]

print("=== Testing imports from main.py ===")
failed_imports = []

for module_name, import_statement in imports_to_test:
    if not test_import(module_name, import_statement):
        failed_imports.append(module_name)
    time.sleep(0.1)  # Small delay between tests

print("\n=== Summary ===")
if failed_imports:
    print(f"Failed imports: {', '.join(failed_imports)}")
else:
    print("All imports successful!")

print("\n=== Testing FastAPI app creation ===")
try:
    from fastapi import FastAPI
    from fastapi.middleware.cors import CORSMiddleware
    
    app = FastAPI(title="Test API")
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    print("✓ FastAPI app created successfully")
except Exception as e:
    print(f"✗ FastAPI app creation failed: {e}")

print("\n=== Testing complete ===")
