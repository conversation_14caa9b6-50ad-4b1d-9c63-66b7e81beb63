#!/usr/bin/env python3
"""
Test script to check if the server starts and responds
"""

import subprocess
import time
import requests
import sys
import os

def test_server_startup():
    print("=== Testing SmartClips Backend Server Startup ===")
    
    # Start the server in background
    print("Starting server...")
    try:
        # Use subprocess to start the server
        process = subprocess.Popen(
            [sys.executable, "main.py"],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            cwd=os.getcwd()
        )
        
        print(f"Server process started with PID: {process.pid}")
        
        # Wait a bit for server to start
        print("Waiting for server to start...")
        time.sleep(10)
        
        # Check if process is still running
        if process.poll() is not None:
            stdout, stderr = process.communicate()
            print(f"Server process exited with code: {process.returncode}")
            print(f"STDOUT: {stdout}")
            print(f"STDERR: {stderr}")
            return False
        
        # Try to make a request to the server
        print("Testing server response...")
        try:
            response = requests.get("http://localhost:8000/", timeout=5)
            print(f"✓ Server responded with status: {response.status_code}")
            print(f"Response: {response.json()}")
            
            # Test health endpoint
            health_response = requests.get("http://localhost:8000/health", timeout=5)
            print(f"✓ Health check responded with status: {health_response.status_code}")
            print(f"Health response: {health_response.json()}")
            
            success = True
            
        except requests.exceptions.RequestException as e:
            print(f"✗ Failed to connect to server: {e}")
            success = False
        
        # Clean up - terminate the server
        print("Terminating server...")
        process.terminate()
        try:
            process.wait(timeout=5)
        except subprocess.TimeoutExpired:
            process.kill()
            process.wait()
        
        return success
        
    except Exception as e:
        print(f"✗ Failed to start server: {e}")
        return False

if __name__ == "__main__":
    success = test_server_startup()
    if success:
        print("\n✓ Server startup test PASSED")
        sys.exit(0)
    else:
        print("\n✗ Server startup test FAILED")
        sys.exit(1)
