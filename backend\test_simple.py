#!/usr/bin/env python3
"""
Simple test server to verify basic FastAPI functionality
"""

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
import os

# Initialize the app
app = FastAPI(title="SmartClips Test API", description="Test API for SmartClips")

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.get("/")
async def root():
    return {"message": "SmartClips Backend is running!", "status": "ok"}

@app.get("/health")
async def health_check():
    return {
        "status": "healthy",
        "ffmpeg_available": os.system("ffmpeg -version") == 0,
        "python_version": "3.11.0"
    }

@app.get("/test-video-processing")
async def test_video_processing():
    try:
        from moviepy.editor import VideoFileClip
        return {"moviepy": "available", "status": "ok"}
    except ImportError as e:
        return {"moviepy": "not available", "error": str(e)}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
