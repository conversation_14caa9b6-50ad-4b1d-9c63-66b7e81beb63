import React, { useEffect, useState, useRef } from "react";
import { useNavigate, useSearchParams } from "react-router-dom";
import { useAuth } from "@/context/AuthContext";
import { Button } from "@/components/ui/button";
import { CheckCircle, Loader2 } from "lucide-react";
import { supabase } from "@/lib/supabase";

const PaymentSuccess: React.FC = () => {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const { user, updateProfile, isLoading } = useAuth();
  const [isProcessing, setIsProcessing] = useState(true);
  const [paymentDetails, setPaymentDetails] = useState<{
    paymentId: string | null;
    plan: string | null;
  }>({ paymentId: null, plan: null });
  const [error, setError] = useState<string | null>(null);
  const effectRan = useRef(false);

  useEffect(() => {
    // 1. WAIT if the authentication context is still loading the user.
    // This is the key to fixing the race condition.
    if (isLoading) {
      console.log("Auth is loading, waiting...");
      return;
    }

    // 2. PREVENT the effect from running more than once.
    if (effectRan.current === true) {
      return;
    }
    effectRan.current = true;

    // We define an async function inside so we can use await.
    const processPayment = async () => {
      console.log("--- Auth has loaded. Processing payment redirect ONCE. ---");

      const sessionId = searchParams.get("session_id");
      const planName = searchParams.get("plan");

      if (!sessionId || !planName) {
        setError("Payment information is missing in the URL.");
        setIsProcessing(false);
        return;
      }

      setPaymentDetails({ paymentId: sessionId, plan: planName });

      // 3. NOW we can safely check for the user.
      if (user) {
        console.log(`User ${user.id} is present. Updating profile...`);
        await updateUserSubscription(planName);
      } else {
        // This case is now a REAL "not logged in" error.
        console.error("Auth loaded, but no user is logged in.");
        setError(
          "Payment processed, but you were not logged in. Please log in and contact support."
        );
      }

      // Clean the URL to prevent re-processing on refresh
      navigate("/payment-success", { replace: true });
      setIsProcessing(false);
    };

    processPayment();

    // We only depend on `isLoading` to trigger this effect when auth state is ready.
  }, [isLoading, user, searchParams, updateProfile, navigate]);

  // Inside the PaymentSuccess component

  const updateUserSubscription = async (planName: string) => {
    if (!user) {
      console.error("Cannot update: User not found.");
      setError(
        "Your session expired. Please log in again and contact support to apply your purchase."
      );
      return;
    }

    // 1. GET the user's current credits before doing anything.
    // Use 0 as a default if they somehow have no credit value.
    const currentCredits = user.credits || 0;
    console.log(`User's current credits: ${currentCredits}`);

    // 2. Determine how many credits TO ADD based on the plan.
    let creditsToAdd = 0;
    let subscriptionPlanName = "free";

    switch (planName.toLowerCase()) {
      case "basic":
        creditsToAdd = 200;
        subscriptionPlanName = "basic";
        break;
      case "pro":
        creditsToAdd = 500;
        subscriptionPlanName = "pro";
        break;
      case "enterprise":
        // For enterprise, you might want to set it to a high number, not add.
        // Let's keep your original logic for this specific case.
        creditsToAdd = 999999 - currentCredits; // This ensures the total becomes 999999
        subscriptionPlanName = "enterprise";
        break;
      default:
        console.error(`Unknown plan name received: ${planName}`);
        setError(
          `An unknown plan (${planName}) was processed. Please contact support.`
        );
        return;
    }

    // 3. CALCULATE the new total.
    const newTotalCredits = currentCredits + creditsToAdd;
    console.log(
      `Adding ${creditsToAdd} credits for a new total of: ${newTotalCredits}`
    );

    try {
      // 4. CALL the central updateProfile function with the NEW TOTAL.
      // This is better than calling Supabase directly as it keeps your React state in sync.
      await updateProfile({
        subscription: subscriptionPlanName,
        credits: newTotalCredits,
        second: newTotalCredits * 60, // Assuming 1 credit = 60 seconds
      });

      console.log("User profile updated successfully via AuthContext.");
    } catch (error) {
      console.error("Failed to update user subscription:", error);
      setError(
        "Your payment was successful, but we failed to update your account. Please contact support."
      );
    }
  };

  const handleContinue = () => {
    navigate("/dashboard");
  };

  if (isProcessing) {
    return (
      <div className="min-h-screen bg-black flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin text-white mx-auto mb-4" />
          <p className="text-white">Processing your payment...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-black flex items-center justify-center p-4">
      <div className="max-w-md w-full bg-white rounded-lg p-8 text-center">
        <CheckCircle className="h-16 w-16 text-green-500 mx-auto mb-6" />

        <h1 className="text-2xl font-bold text-gray-900 mb-4">
          Payment Successful!
        </h1>

        <p className="text-gray-600 mb-6">
          Thank you for your purchase. Your subscription has been activated.
        </p>

        {/* {paymentDetails && (
          <div className="bg-gray-50 rounded-lg p-4 mb-6 text-left">
            <h3 className="font-semibold text-gray-900 mb-2">
              Payment Details:
            </h3>
            {paymentDetails.paymentId && (
              <p className="text-sm text-gray-600">
                <strong>Payment ID:</strong> {paymentDetails.paymentId}
              </p>
            )}
            {paymentDetails.amount && (
              <p className="text-sm text-gray-600">
                <strong>Amount:</strong> ${paymentDetails.amount}
              </p>
            )}
            {paymentDetails.plan && (
              <p className="text-sm text-gray-600">
                <strong>Plan:</strong> {paymentDetails.plan}
              </p>
            )}
          </div>
        )} */}

        <Button
          onClick={handleContinue}
          className="w-full bg-gradient-to-r from-purple-600 to-blue-600 hover:opacity-90"
        >
          Continue to Dashboard
        </Button>

        <p className="text-xs text-gray-500 mt-4">
          You will receive a confirmation email shortly.
        </p>
      </div>
    </div>
  );
};

export default PaymentSuccess;
