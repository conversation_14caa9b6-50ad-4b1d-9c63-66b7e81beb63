import React, { useState, useRef, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Slider } from "@/components/ui/slider";
import {
  <PERSON>,
  Pause,
  Ski<PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>py,
  Trash2,
  Plus,
  ZoomIn,
  ZoomOut,
} from "lucide-react";

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

interface TimelineClip {
  id: string;
  start: number;
  end: number;
  duration: number;
  url: string;
  title: string;
  track: number;
}
interface TimelineSegment {
  id: string;
  sourceStart: number;
  sourceEnd: number;
  duration: number;
  virtualStart: number;
  virtualEnd: number;
}
interface TimelineProps {
  clips: TimelineClip[];
  segments: TimelineSegment[];
  duration: number;
  currentTime: number;

  isPlaying: boolean;
  onTimeChange: (time: number) => void;
  onPlay: () => void;
  onPause: () => void;
  onClipUpdate: (clipId: string, updates: Partial<TimelineClip>) => void;
  onClipDelete: (clipId: string) => void;
  onClipAdd: (clip: Omit<TimelineClip, "id">) => void;
  onClipDuplicate: (clipId: string) => void;
  onClipSplit: (clipId: string, time: number) => void;
  // videoRef: React.RefObject<HTMLVideoElement>;
  onSpeedChange: (rate: number) => void;
}

const Timeline: React.FC<TimelineProps> = ({
  clips,
  segments,
  duration,
  currentTime,
  isPlaying,
  onTimeChange,
  onPlay,
  onPause,
  onClipUpdate,
  onClipDelete,
  onClipAdd,
  onClipDuplicate,
  onClipSplit,
  onSpeedChange,
  // videoRef,
}) => {
  const [zoom, setZoom] = useState(1);
  const [selectedClip, setSelectedClip] = useState<string | null>(null);
  const [dragState, setDragState] = useState<{
    clipId: string;
    type: "move" | "resize-start" | "resize-end";
    startX: number;
    startTime: number;
  } | null>(null);

  const [playbackRate, setPlaybackRate] = useState(1);

  useEffect(() => {
    // This effect now calls the parent's function when the speed changes.
    onSpeedChange(playbackRate);
  }, [playbackRate, onSpeedChange]);

  const timelineRef = useRef<HTMLDivElement>(null);
  const TRACK_HEIGHT = 60;
  const TIMELINE_HEIGHT = 200;
  const PIXELS_PER_SECOND = 50 * zoom;

  // Convert time to pixel position
  const timeToPixels = (time: number) => time * PIXELS_PER_SECOND;

  // Convert pixel position to time
  const pixelsToTime = (pixels: number) => pixels / PIXELS_PER_SECOND;

  // Format time for display
  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, "0")}`;
  };

  const totalPlayableDuration = clips.reduce(
    (total, clip) => total + clip.duration,
    0
  );

  // Handle timeline click
  const handleTimelineClick = (e: React.MouseEvent) => {
    if (!timelineRef.current || dragState) return;

    const rect = timelineRef.current.getBoundingClientRect();
    const x = e.clientX - rect.left;
    const time = pixelsToTime(x);
    onTimeChange(Math.max(0, Math.min(duration, time)));
  };

  // Handle clip mouse down
  const handleClipMouseDown = (
    e: React.MouseEvent,
    clipId: string,
    type: "move" | "resize-start" | "resize-end"
  ) => {
    e.stopPropagation();
    const clip = clips.find((c) => c.id === clipId);
    if (!clip) return;

    setDragState({
      clipId,
      type,
      startX: e.clientX,
      startTime: type === "resize-end" ? clip.end : clip.start,
    });
    setSelectedClip(clipId);
  };

  useEffect(() => {
    if (!timelineRef.current || !isPlaying) return;

    const timeline = timelineRef.current;
    const pixelsPerSecond = 50 * zoom; // Make sure this matches your calculation
    const playheadPosition = currentTime * pixelsPerSecond;

    // Get the visible width of the timeline
    const visibleWidth = timeline.clientWidth;
    // Get the current scroll position
    const scrollLeft = timeline.scrollLeft;

    // Define a "safe zone" in the middle of the timeline (e.g., 40% to 60%)
    const safeZoneStart = scrollLeft + visibleWidth * 0.4;
    const safeZoneEnd = scrollLeft + visibleWidth * 0.6;

    // If the playhead moves outside the safe zone, adjust the scroll
    if (playheadPosition < safeZoneStart || playheadPosition > safeZoneEnd) {
      // Center the playhead in the view
      const targetScrollLeft = playheadPosition - visibleWidth / 2;

      timeline.scrollTo({
        left: targetScrollLeft,
        behavior: "smooth", // Use 'smooth' for a nice scrolling effect
      });
    }
  }, [currentTime, isPlaying, zoom]);

  // Handle mouse move during drag
  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      if (!dragState || !timelineRef.current) return;

      const deltaX = e.clientX - dragState.startX;
      const deltaTime = pixelsToTime(deltaX);
      const clip = clips.find((c) => c.id === dragState.clipId);
      if (!clip) return;

      let updates: Partial<TimelineClip> = {};

      switch (dragState.type) {
        case "move":
          const newStart = Math.max(0, dragState.startTime + deltaTime);
          const newEnd = newStart + clip.duration;
          if (newEnd <= duration) {
            updates = { start: newStart, end: newEnd };
          }
          break;

        case "resize-start":
          const newStartTime = Math.max(
            0,
            Math.min(clip.end - 0.1, dragState.startTime + deltaTime)
          );
          updates = {
            start: newStartTime,
            duration: clip.end - newStartTime,
          };
          break;

        case "resize-end":
          const newEndTime = Math.max(
            clip.start + 0.1,
            Math.min(duration, dragState.startTime + deltaTime)
          );
          updates = {
            end: newEndTime,
            duration: newEndTime - clip.start,
          };
          break;
      }

      if (Object.keys(updates).length > 0) {
        onClipUpdate(dragState.clipId, updates);
      }
    };

    const handleMouseUp = () => {
      setDragState(null);
    };

    if (dragState) {
      document.addEventListener("mousemove", handleMouseMove);
      document.addEventListener("mouseup", handleMouseUp);
    }

    return () => {
      document.removeEventListener("mousemove", handleMouseMove);
      document.removeEventListener("mouseup", handleMouseUp);
    };
  }, [dragState, clips, duration, onClipUpdate]);

  // Render time markers
  const renderTimeMarkers = () => {
    const markers = [];
    const interval = zoom < 0.5 ? 10 : zoom < 1 ? 5 : 1;

    for (let time = 0; time <= duration; time += interval) {
      const x = timeToPixels(time);
      markers.push(
        <div
          key={time}
          className="absolute top-0 bottom-0 border-l border-gray-300"
          style={{ left: x }}
        >
          <span className="absolute -top-6 -left-4 text-xs text-gray-500">
            {formatTime(time)}
          </span>
        </div>
      );
    }

    return markers;
  };

  // Render clips
  const renderClips = () => {
    // We map over SEGMENTS to get the correct VISUAL position and width
    return segments.map((segment) => {
      // Find the original clip data using the ID from the segment
      const originalClip = clips.find((c) => c.id === segment.id);

      // If for some reason the original clip is gone, don't render this segment
      if (!originalClip) return null;

      // Use the VIRTUAL start time for the left position
      const left = timeToPixels(segment.virtualStart);

      // Use the segment's duration for the width
      const width = timeToPixels(segment.duration);

      // Other properties are the same as before
      const top = originalClip.track * TRACK_HEIGHT + 30;
      const isSelected = selectedClip === segment.id;

      return (
        <div
          key={segment.id}
          className={`absolute bg-blue-500 rounded border-2 cursor-move select-none ${
            isSelected ? "border-blue-300 shadow-lg" : "border-blue-600"
          }`}
          style={{
            left,
            width,
            top,
            height: TRACK_HEIGHT - 10,
          }}
          // The event handler uses the ID, which is consistent
          onMouseDown={(e) => handleClipMouseDown(e, segment.id, "move")}
        >
          {/* Resize handles also use the ID */}
          <div
            className="absolute left-0 top-0 bottom-0 w-2 bg-blue-300 cursor-ew-resize opacity-0 hover:opacity-100"
            onMouseDown={(e) =>
              handleClipMouseDown(e, segment.id, "resize-start")
            }
          />
          <div
            className="absolute right-0 top-0 bottom-0 w-2 bg-blue-300 cursor-ew-resize opacity-0 hover:opacity-100"
            onMouseDown={(e) =>
              handleClipMouseDown(e, segment.id, "resize-end")
            }
          />

          {/* Clip content - now we can get the title from the original clip */}
          <div className="p-2 text-white text-xs truncate">
            <div className="font-medium">{originalClip.title}</div>
            <div className="opacity-75">{formatTime(segment.duration)}</div>
          </div>
        </div>
      );
    });
  };

  // Render playhead
  const renderPlayhead = () => {
    const x = timeToPixels(currentTime);
    return (
      <div
        className="absolute top-0 bottom-0 w-0.5 bg-red-500 z-10 pointer-events-none"
        style={{ left: x }}
      >
        <div className="absolute -top-2 -left-2 w-4 h-4 bg-red-500 rounded-full" />
      </div>
    );
  };

  return (
    <div className="space-y-4">
      {/* Transport Controls */}
      <Card>
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="icon"
                onClick={() => onTimeChange(Math.max(0, currentTime - 10))}
              >
                <SkipBack className="h-4 w-4" />
              </Button>

              <Button
                variant="outline"
                size="icon"
                onClick={isPlaying ? onPause : onPlay}
              >
                {isPlaying ? (
                  <Pause className="h-4 w-4" />
                ) : (
                  <Play className="h-4 w-4" />
                )}
              </Button>

              <Button
                variant="outline"
                size="icon"
                onClick={() =>
                  onTimeChange(Math.min(duration, currentTime + 10))
                }
              >
                <SkipForward className="h-4 w-4" />
              </Button>
            </div>

            <div className="flex items-center gap-2">
              <span className="text-sm font-mono">
                {formatTime(currentTime)} / {formatTime(totalPlayableDuration)}
              </span>
            </div>

            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="icon"
                onClick={() => setZoom(Math.max(0.1, zoom - 0.2))}
              >
                <ZoomOut className="h-4 w-4" />
              </Button>

              <span className="text-sm w-12 text-center">
                {Math.round(zoom * 100)}%
              </span>

              <Button
                variant="outline"
                size="icon"
                onClick={() => setZoom(Math.min(3, zoom + 0.2))}
              >
                <ZoomIn className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Timeline */}
      <Card>
        <CardContent className="p-0">
          <div
            ref={timelineRef}
            className="relative bg-gray-50 overflow-x-auto cursor-crosshair"
            style={{
              height: TIMELINE_HEIGHT,
            }}
            onClick={handleTimelineClick}
          >
            {/* This inner div has a calculated width, forcing the outer div to scroll */}
            <div
              className="relative h-full"
              style={{
                width: timeToPixels(duration),
              }}
            >
              {renderTimeMarkers()}
              {renderClips()}
              {renderPlayhead()}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Clip Controls */}
      {selectedClip && (
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between flex-wrap gap-4">
              {/* Left Side: Selected Clip Title */}
              <div className="text-sm font-medium">
                Selected: {clips.find((c) => c.id === selectedClip)?.title}
              </div>

              {/* Right Side: Button Groups */}
              <div className="flex items-center gap-4">
                {/* Group 1: Editing Tools */}
                <div className="flex items-center gap-2">
                  {/* <Button variant="outline" size="sm"> */}
                  {/* You should use a unique icon for each action */}
                  {/* <Scissors className="h-4 w-4 mr-1" />
                    Trim
                  </Button> */}
                  {/* <Button variant="outline" size="sm">
                    <Copy className="h-4 w-4 mr-1" />
                    Ratio
                  </Button> */}
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="outline" size="sm">
                        <Play className="h-4 w-4 mr-1" />
                        Speed ({playbackRate}x)
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      {[0.5, 0.75, 1, 1.25, 1.5, 2].map((rate) => (
                        <DropdownMenuItem
                          key={rate}
                          onClick={() => setPlaybackRate(rate)}
                        >
                          {rate}x {rate === 1 && "(Normal)"}
                        </DropdownMenuItem>
                      ))}
                    </DropdownMenuContent>
                  </DropdownMenu>
                  <Button variant="outline" size="sm">
                    <Copy className="h-4 w-4 mr-1" />
                    Filters
                  </Button>
                </div>

                {/* Group 2: Core Actions (Icon Buttons) */}
                <div className="flex items-center gap-2">
                  <Button
                    variant="outline"
                    size="icon"
                    onClick={() => onClipDuplicate(selectedClip)}
                    title="Duplicate" // Tooltip for accessibility
                  >
                    <Copy className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="outline"
                    size="icon"
                    onClick={() => onClipSplit(selectedClip, currentTime)}
                    title="Split at Playhead"
                  >
                    <Scissors className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="destructive" // Use a destructive variant for delete
                    size="icon"
                    onClick={() => {
                      onClipDelete(selectedClip);
                      setSelectedClip(null);
                    }}
                    title="Delete"
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default Timeline;
