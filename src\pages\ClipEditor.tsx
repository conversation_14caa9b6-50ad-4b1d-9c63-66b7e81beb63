import React, { useState, useEffect, useRef, useMemo } from "react";
import { use<PERSON>ara<PERSON>, useNavigate } from "react-router-dom";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import {
  ArrowLeft,
  Download,
  Play,
  Pause,
  Save,
  Type,
  Palette,
  Volume2,
  Scissors,
  RotateCcw,
  Share2,
  Settings,
} from "lucide-react";
import { toast } from "@/hooks/use-toast";
import DashboardLayout from "@/components/Dashboard";
import Timeline from "@/components/editor/Timeline";
import { useAuth } from "@/context/AuthContext";
import { Navigate } from "react-router-dom";
import {
  Select,
  <PERSON><PERSON>ontent,
  <PERSON>I<PERSON>,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Slider } from "@/components/ui/slider";
import { supabase } from "@/lib/supabase";

interface TimelineClip {
  id: string;
  start: number;
  end: number;
  duration: number;
  url: string;
  title: string;
  track: number;
}

interface TextOverlay {
  id: string;
  text: string;
  x: number;
  y: number;
  fontSize: number;
  color: string;
  startTime: number;
  endTime: number;
}

interface TimelineSegment {
  id: string;
  sourceStart: number;
  sourceEnd: number;
  duration: number;
  virtualStart: number;
  virtualEnd: number;
}

const ClipEditor = () => {
  const { isAuthenticated } = useAuth();
  const { clipId } = useParams();
  const navigate = useNavigate();

  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [exporting, setExporting] = useState(false);
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(60);
  const [virtualTime, setVirtualTime] = useState(0);
  const [sourceDuration, setSourceDuration] = useState(60);

  // Timeline state
  const [clips, setClips] = useState<TimelineClip[]>([]);
  const [textOverlays, setTextOverlays] = useState<TextOverlay[]>([]);
  // const videoRef = useRef<HTMLVideoElement>(null);
  const videoRef = useRef<HTMLVideoElement>(null);

  // Editing state
  const [clipTitle, setClipTitle] = useState("");
  const [clipDescription, setClipDescription] = useState("");
  const [selectedTextOverlay, setSelectedTextOverlay] = useState<string | null>(
    null
  );
  const [newTextOverlay, setNewTextOverlay] = useState({
    text: "",
    fontSize: 24,
    color: "#ffffff",
    startTime: 0,
    endTime: 5,
  });

  const { timelineSegments, virtualDuration } = useMemo(() => {
    const sortedClips = [...clips].sort((a, b) => a.start - b.start);

    let currentVirtualTime = 0;
    const segments: TimelineSegment[] = [];

    for (const clip of sortedClips) {
      const duration = clip.end - clip.start;
      segments.push({
        id: clip.id,
        sourceStart: clip.start,
        sourceEnd: clip.end,
        duration: duration,
        virtualStart: currentVirtualTime,
        virtualEnd: currentVirtualTime + duration,
      });
      currentVirtualTime += duration;
    }

    return { timelineSegments: segments, virtualDuration: currentVirtualTime };
  }, [clips]);

  // --- MODIFICATION 2: MAPPING FUNCTIONS ---
  const mapVirtualToSourceTime = (vTime: number): number | null => {
    const segment = timelineSegments.find(
      (s) => vTime >= s.virtualStart && vTime < s.virtualEnd
    );
    if (!segment) return null; // Time is in a virtual gap (shouldn't happen with continuous timeline) or outside bounds

    const offset = vTime - segment.virtualStart;
    return segment.sourceStart + offset;
  };

  if (!isAuthenticated) {
    return <Navigate to="/login" />;
  }

  useEffect(() => {
    loadClip();
  }, [clipId]);

  const loadClip = async () => {
    try {
      setLoading(true);

      const { data, error } = await supabase
        .from("myclip")
        .select("*")
        .eq("id", clipId)
        .single();

      if (error || !data) {
        throw error || new Error("Clip not found");
      }

      const fetchedClip = {
        id: data.id,
        title: data.title || "",
        description: data.text || "",
        url: data.url,
        duration: data.end_time - data.start_time,
        vitalityScore: data.score || 0,
      };

      const fetchedTimelineClip: TimelineClip = {
        id: "main-clip",
        start: data.start_time,
        end: data.end_time,
        duration: data.end_time - data.start_time,
        url: data.url,
        title: data.title || "Clip",
        track: 0,
      };

      setClipTitle(fetchedClip.title);
      setClipDescription(fetchedClip.description);
      setDuration(fetchedClip.duration);
      setClips([fetchedTimelineClip]);
      setTextOverlays([]);
    } catch (error) {
      console.error("Error loading clip:", error);
      toast({
        title: "Error loading clip",
        description: "Failed to load the clip for editing",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  // --- MODIFICATION: LOOPING LOGIC ---
  const handleTimeUpdate = () => {
    if (!videoRef.current) return;

    const sourceTime = videoRef.current.currentTime;

    // Find which segment of the source video we are currently in
    const currentSegment = timelineSegments.find(
      (s) => sourceTime >= s.sourceStart && sourceTime < s.sourceEnd
    );

    if (currentSegment) {
      // We are in a valid segment, calculate the virtual time for the UI
      const offset = sourceTime - currentSegment.sourceStart;
      setVirtualTime(currentSegment.virtualStart + offset);
    } else if (isPlaying) {
      // We are in a GAP, find the next segment to jump to
      const nextSegment = timelineSegments.find(
        (s) => s.sourceStart > sourceTime
      );
      if (nextSegment) {
        videoRef.current.currentTime = nextSegment.sourceStart;
      } else {
        // End of timeline, loop back
        if (timelineSegments.length > 0) {
          videoRef.current.currentTime = timelineSegments[0].sourceStart;
        } else {
          handlePause();
        }
      }
    }
  };

  const handlePlay = () => {
    if (!videoRef.current || timelineSegments.length === 0) return;

    // If paused at the very end, loop to the beginning
    if (virtualTime >= virtualDuration) {
      handleTimeChange(0); // This will seek the source video correctly
    }

    setIsPlaying(true);
    videoRef.current.play();
  };

  const handlePause = () => {
    setIsPlaying(false);
    videoRef.current?.pause();
  };
  const handleSpeedChange = (rate: number) => {
    if (videoRef.current) {
      videoRef.current.playbackRate = rate;
    }
  };
  // const handlePause = () => {
  //   setIsPlaying(false);
  //   videoRef.current?.pause();
  // };

  const handleTimeChange = (vTime: number) => {
    const newSourceTime = mapVirtualToSourceTime(vTime);
    if (videoRef.current && newSourceTime !== null) {
      videoRef.current.currentTime = newSourceTime;
    }
    setVirtualTime(vTime);
  };

  const handleClipUpdate = (clipId: string, updates: Partial<TimelineClip>) => {
    setClips((prev) =>
      prev.map((clip) => (clip.id === clipId ? { ...clip, ...updates } : clip))
    );
  };

  const handleClipDelete = (clipId: string) => {
    setClips((prev) => prev.filter((clip) => clip.id !== clipId));
  };

  const handleClipAdd = (clip: Omit<TimelineClip, "id">) => {
    const newClip: TimelineClip = {
      ...clip,
      id: `clip-${Date.now()}`,
    };
    setClips((prev) => [...prev, newClip]);
  };

  const handleClipDuplicate = (clipId: string) => {
    const clipToDuplicate = clips.find((c) => c.id === clipId);
    if (!clipToDuplicate) return;

    const newClip: TimelineClip = {
      ...clipToDuplicate,
      id: `clip-${Date.now()}`,
      start: clipToDuplicate.end,
      end: clipToDuplicate.end + clipToDuplicate.duration,
    };

    if (newClip.end > duration) {
      toast({
        title: "Cannot duplicate",
        description: "Not enough space on timeline.",
      });
      return;
    }

    setClips((prev) => [...prev, newClip]);
    toast({ title: "Clip duplicated" });
  };

  const handleClipSplit = (clipId: string, time: number) => {
    const clipToSplit = clips.find((c) => c.id === clipId);
    if (!clipToSplit) return;

    if (time <= clipToSplit.start + 0.1 || time >= clipToSplit.end - 0.1) {
      toast({
        title: "Cannot split",
        description: "Playhead must be inside the clip.",
      });
      return;
    }

    const firstPart = {
      ...clipToSplit,
      end: time,
      duration: time - clipToSplit.start,
    };

    const secondPart: TimelineClip = {
      ...clipToSplit,
      id: `clip-${Date.now()}`,
      start: time,
      duration: clipToSplit.end - time,
    };

    setClips((prev) => [
      ...prev.filter((c) => c.id !== clipId),
      firstPart,
      secondPart,
    ]);
    toast({ title: "Clip split" });
  };

  const addTextOverlay = () => {
    // ... This function now works with virtual times naturally
    if (!newTextOverlay.text.trim()) return;
    const overlay: TextOverlay = {
      id: `text-${Date.now()}`,
      ...newTextOverlay,
      startTime: virtualTime, // Set start time based on current virtual time
      endTime:
        virtualTime + 5 > virtualDuration ? virtualDuration : virtualTime + 5,
      x: 50,
      y: 50,
    };
    setTextOverlays((prev) => [...prev, overlay]);
    setNewTextOverlay({
      text: "",
      fontSize: 24,
      color: "#ffffff",
      startTime: 0,
      endTime: 5,
    });
  };
  const removeTextOverlay = (id: string) => {
    setTextOverlays((prev) => prev.filter((overlay) => overlay.id !== id));
    if (selectedTextOverlay === id) {
      setSelectedTextOverlay(null);
    }
  };

  const handleSave = async () => {
    try {
      setSaving(true);
      await new Promise((resolve) => setTimeout(resolve, 1000));
      toast({
        title: "Clip saved",
        description: "Your changes have been saved successfully",
      });
    } catch (error) {
      toast({
        title: "Save failed",
        description: "Failed to save your changes",
        variant: "destructive",
      });
    } finally {
      setSaving(false);
    }
  };

  const handleExport = async () => {
    try {
      setExporting(true);
      await new Promise((resolve) => setTimeout(resolve, 3000));
      toast({
        title: "Export complete",
        description: "Your clip has been exported successfully",
      });
    } catch (error) {
      toast({
        title: "Export failed",
        description: "Failed to export your clip",
        variant: "destructive",
      });
    } finally {
      setExporting(false);
    }
  };

  if (loading) {
    return (
      <DashboardLayout>
        <div className="container mx-auto py-8">
          <div className="flex items-center justify-center h-64">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="container mx-auto py-8 px-4">
        <div className="space-y-6">
          {/* Header */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <Button
                variant="outline"
                size="icon"
                onClick={() => navigate("/clip-results")}
              >
                <ArrowLeft className="h-4 w-4" />
              </Button>
              <div>
                <h1 className="text-2xl font-bold">Clip Editor</h1>
                <p className="text-muted-foreground">
                  Edit and enhance your video clip
                </p>
              </div>
            </div>

            <div className="flex gap-2">
              <Button variant="outline" onClick={handleSave} disabled={saving}>
                {saving ? (
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current mr-2" />
                ) : (
                  <Save className="h-4 w-4 mr-2" />
                )}
                Save
              </Button>
              <Button onClick={handleExport} disabled={exporting}>
                {exporting ? (
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                ) : (
                  <Download className="h-4 w-4 mr-2" />
                )}
                Export
              </Button>
            </div>
          </div>

          {/* Main Editor Layout */}
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
            {/* Video Preview */}
            <div className="lg:col-span-3 space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Preview</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="relative bg-black rounded-lg aspect-video">
                    <video
                      ref={videoRef}
                      src={clips[0]?.url}
                      className="w-full h-full object-contain rounded-lg"
                      controls={false}
                      onTimeUpdate={handleTimeUpdate}
                    />

                    {/* Text Overlays Preview */}
                    {textOverlays
                      .filter(
                        (overlay) =>
                          currentTime >= overlay.startTime &&
                          currentTime <= overlay.endTime
                      )
                      .map((overlay) => (
                        <div
                          key={overlay.id}
                          className="absolute pointer-events-none"
                          style={{
                            left: `${overlay.x}%`,
                            top: `${overlay.y}%`,
                            fontSize: `${overlay.fontSize}px`,
                            color: overlay.color,
                            fontWeight: "bold",
                            textShadow: "2px 2px 4px rgba(0,0,0,0.8)",
                            transform: "translate(-50%, -50%)",
                          }}
                        >
                          {overlay.text}
                        </div>
                      ))}
                  </div>
                </CardContent>
              </Card>

              {/* Timeline */}
              <Timeline
                clips={clips} // Pass original clips for rendering if needed, or modify timeline to use segments
                duration={virtualDuration}
                segments={timelineSegments}
                currentTime={virtualTime}
                isPlaying={isPlaying}
                onTimeChange={handleTimeChange}
                onPlay={handlePlay}
                onPause={handlePause}
                onClipUpdate={handleClipUpdate}
                onClipDelete={handleClipDelete}
                onClipAdd={() => {}}
                onClipDuplicate={() => {}}
                onClipSplit={handleClipSplit} // Pass the new handler
                onSpeedChange={handleSpeedChange}
              />
            </div>

            {/* Editing Tools */}
            <div className="space-y-4">
              {/* Clip Info */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Settings className="h-4 w-4" />
                    Clip Details
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <label className="text-sm font-medium">Title</label>
                    <Input
                      value={clipTitle}
                      onChange={(e) => setClipTitle(e.target.value)}
                      placeholder="Enter clip title..."
                    />
                  </div>
                  <div>
                    <label className="text-sm font-medium">Description</label>
                    <Textarea
                      value={clipDescription}
                      onChange={(e) => setClipDescription(e.target.value)}
                      placeholder="Enter clip description..."
                      rows={3}
                    />
                  </div>
                </CardContent>
              </Card>

              {/* Text Overlays */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Type className="h-4 w-4" />
                    Text Overlays
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {/* Add New Text */}
                  <div className="space-y-3">
                    <Input
                      placeholder="Enter text..."
                      value={newTextOverlay.text}
                      onChange={(e) =>
                        setNewTextOverlay((prev) => ({
                          ...prev,
                          text: e.target.value,
                        }))
                      }
                    />

                    <div className="grid grid-cols-2 gap-2">
                      <div>
                        <label className="text-xs text-muted-foreground">
                          Font Size
                        </label>
                        <Slider
                          value={[newTextOverlay.fontSize]}
                          onValueChange={(value) =>
                            setNewTextOverlay((prev) => ({
                              ...prev,
                              fontSize: value[0],
                            }))
                          }
                          min={12}
                          max={48}
                          step={2}
                        />
                        <div className="text-xs text-center">
                          {newTextOverlay.fontSize}px
                        </div>
                      </div>

                      <div>
                        <label className="text-xs text-muted-foreground">
                          Color
                        </label>
                        <Input
                          type="color"
                          value={newTextOverlay.color}
                          onChange={(e) =>
                            setNewTextOverlay((prev) => ({
                              ...prev,
                              color: e.target.value,
                            }))
                          }
                          className="h-8"
                        />
                      </div>
                    </div>

                    <div className="grid grid-cols-2 gap-2">
                      <div>
                        <label className="text-xs text-muted-foreground">
                          Start (s)
                        </label>
                        <Input
                          type="number"
                          value={newTextOverlay.startTime}
                          onChange={(e) =>
                            setNewTextOverlay((prev) => ({
                              ...prev,
                              startTime: Number(e.target.value),
                            }))
                          }
                          min={0}
                          max={duration}
                        />
                      </div>
                      <div>
                        <label className="text-xs text-muted-foreground">
                          End (s)
                        </label>
                        <Input
                          type="number"
                          value={newTextOverlay.endTime}
                          onChange={(e) =>
                            setNewTextOverlay((prev) => ({
                              ...prev,
                              endTime: Number(e.target.value),
                            }))
                          }
                          min={0}
                          max={duration}
                        />
                      </div>
                    </div>

                    <Button
                      onClick={addTextOverlay}
                      className="w-full"
                      size="sm"
                    >
                      Add Text Overlay
                    </Button>
                  </div>

                  {/* Existing Overlays */}
                  <div className="space-y-2">
                    {textOverlays.map((overlay) => (
                      <div
                        key={overlay.id}
                        className="flex items-center justify-between p-2 border rounded"
                      >
                        <div className="flex-1">
                          <div className="text-sm font-medium truncate">
                            {overlay.text}
                          </div>
                          <div className="text-xs text-muted-foreground">
                            {overlay.startTime}s - {overlay.endTime}s
                          </div>
                        </div>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => removeTextOverlay(overlay.id)}
                        >
                          ×
                        </Button>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              {/* Export Options */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Download className="h-4 w-4" />
                    Export Options
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <label className="text-sm font-medium">Quality</label>
                    <Select defaultValue="1080p">
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="4k">4K (3840x2160)</SelectItem>
                        <SelectItem value="1080p">1080p (1920x1080)</SelectItem>
                        <SelectItem value="720p">720p (1280x720)</SelectItem>
                        <SelectItem value="480p">480p (854x480)</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <label className="text-sm font-medium">Format</label>
                    <Select defaultValue="mp4">
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="mp4">MP4</SelectItem>
                        <SelectItem value="mov">MOV</SelectItem>
                        <SelectItem value="avi">AVI</SelectItem>
                        <SelectItem value="webm">WebM</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <Button
                    onClick={handleExport}
                    className="w-full"
                    disabled={exporting}
                  >
                    {exporting ? "Exporting..." : "Export Video"}
                  </Button>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
};

export default ClipEditor;
