#!/usr/bin/env python3
"""
Minimal SmartClips Backend for Testing Core Functionality
"""

import os
import tempfile
import logging
from typing import List, Dict, Any, Optional
from fastapi import FastAPI, File, UploadFile, Form, HTTPException, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
import video_processing
import storage

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize the app
app = FastAPI(title="SmartClips Minimal API", description="Minimal API for SmartClips video processing")

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Directory for temporary files
TEMP_DIR = "temp"
os.makedirs(TEMP_DIR, exist_ok=True)

# Models
class VideoSegment(BaseModel):
    start_time: float
    end_time: float
    text: str

class ProcessedVideo(BaseModel):
    segments: List[VideoSegment]
    video_urls: List[str]

class URLProcessRequest(BaseModel):
    url: str
    min_duration: float = 10.0
    max_duration: float = 60.0
    max_clips: Optional[int] = 10

@app.get("/")
async def root():
    return {"message": "SmartClips Minimal Backend is running!", "status": "ok"}

@app.get("/health")
async def health_check():
    return {
        "status": "healthy",
        "ffmpeg_available": os.system("ffmpeg -version > nul 2>&1") == 0,
        "temp_dir": TEMP_DIR,
        "temp_dir_exists": os.path.exists(TEMP_DIR)
    }

@app.post("/upload-simple", response_model=ProcessedVideo)
async def upload_video_simple(
    background_tasks: BackgroundTasks,
    file: UploadFile = File(...),
    min_duration: float = Form(10.0),
    max_duration: float = Form(60.0)
):
    """Simple video upload and processing endpoint"""
    try:
        # Save the uploaded video temporarily
        temp_video_path = os.path.join(TEMP_DIR, file.filename)
        with open(temp_video_path, "wb") as temp_video:
            content = await file.read()
            temp_video.write(content)

        logger.info(f"Video saved to: {temp_video_path}")

        # Process the video
        transcript, timestamps = video_processing.transcribe_video(temp_video_path, TEMP_DIR)
        logger.info(f"Transcript extracted: {len(transcript)} characters")

        segments = video_processing.segment_transcript(
            transcript,
            timestamps,
            min_duration=min_duration,
            max_duration=max_duration,
            refine_with_ai=False  # Disable AI for minimal version
        )
        logger.info(f"Created {len(segments)} segments")

        clipped_videos = video_processing.clip_video_from_text(temp_video_path, segments, TEMP_DIR)
        logger.info(f"Created {len(clipped_videos)} video clips")

        # Upload to Cloudinary
        video_urls = []
        for clip_path in clipped_videos:
            try:
                url = storage.upload_to_cloudinary(clip_path)
                video_urls.append(url)
                logger.info(f"Uploaded clip: {url}")
            except Exception as e:
                logger.error(f"Failed to upload clip {clip_path}: {e}")

        # Clean up in background
        def cleanup():
            try:
                if os.path.exists(temp_video_path):
                    os.remove(temp_video_path)
                for clip in clipped_videos:
                    if os.path.exists(clip):
                        os.remove(clip)
                logger.info("Cleanup completed")
            except Exception as e:
                logger.error(f"Cleanup error: {str(e)}")

        background_tasks.add_task(cleanup)

        return ProcessedVideo(
            segments=[
                VideoSegment(start_time=s["start"], end_time=s["end"], text=s["text"])
                for s in segments
            ],
            video_urls=video_urls
        )

    except Exception as e:
        logger.error(f"Upload error: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/process-url-simple")
async def process_url_simple(request: URLProcessRequest, background_tasks: BackgroundTasks):
    """Simple URL processing endpoint"""
    try:
        import url_processor
        
        # Download video
        processor = url_processor.URLVideoProcessor(TEMP_DIR)
        validation = processor.validate_url(request.url)
        
        if not validation['valid']:
            raise HTTPException(status_code=400, detail=validation['error'])
        
        video_path, download_metadata = processor.download_video(request.url, "best")
        logger.info(f"Downloaded video: {video_path}")

        # Process the video
        transcript, timestamps = video_processing.transcribe_video(video_path, TEMP_DIR)
        segments = video_processing.segment_transcript(
            transcript,
            timestamps,
            min_duration=request.min_duration,
            max_duration=request.max_duration,
            refine_with_ai=False
        )

        if request.max_clips:
            segments = segments[:request.max_clips]

        clipped_videos = video_processing.clip_video_from_text(video_path, segments, TEMP_DIR)

        # Upload clips
        video_urls = []
        for clip_path in clipped_videos:
            try:
                url = storage.upload_to_cloudinary(clip_path)
                video_urls.append(url)
            except Exception as e:
                logger.error(f"Failed to upload clip: {e}")

        # Cleanup
        def cleanup():
            try:
                processor.cleanup_downloaded_file(video_path)
                for clip_path in clipped_videos:
                    if os.path.exists(clip_path):
                        os.remove(clip_path)
            except Exception as e:
                logger.error(f"Cleanup error: {str(e)}")

        background_tasks.add_task(cleanup)

        return {
            "clips": [
                {
                    "url": url,
                    "text": segment["text"],
                    "start_time": segment["start"],
                    "end_time": segment["end"]
                }
                for segment, url in zip(segments, video_urls)
            ]
        }

    except Exception as e:
        logger.error(f"URL processing error: {e}")
        raise HTTPException(status_code=500, detail=str(e))

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
