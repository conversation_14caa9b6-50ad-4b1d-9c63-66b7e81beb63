import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { useAuth } from "@/context/AuthContext";

interface Plan {
  name: string;
  price: number;
  clips: string;
  isPopular?: boolean;
  buttonText: string;
  buttonStyle: "basic" | "pro" | "enterprise";
}

const PaymentPlanComponent: React.FC = () => {
  const [isYearlyBilling, setIsYearlyBilling] = useState<boolean>(false);
  const navigate = useNavigate();
  const { isAuthenticated, isLoading, user } = useAuth();

  // Set global styles to remove default margins and ensure black background
  useEffect(() => {
    // Store original styles
    const originalBodyStyle = document.body.style.cssText;
    const originalHtmlStyle = document.documentElement.style.cssText;

    // Apply black background styles
    document.body.style.margin = "0";
    document.body.style.padding = "0";
    document.body.style.backgroundColor = "#000000";
    document.documentElement.style.backgroundColor = "#000000";

    // Cleanup function to restore original styles
    return () => {
      document.body.style.cssText = originalBodyStyle;
      document.documentElement.style.cssText = originalHtmlStyle;
    };
  }, []);

  // Optional: Redirect authenticated users with active plans to dashboard
  // useEffect(() => {
  //   // Only redirect if user is authenticated AND has an active plan
  //   if (!isLoading && isAuthenticated && user) {
  //     const hasActivePlan =
  //       user.subscription &&
  //       user.subscription !== "free" &&
  //       user.subscription !== null;
  //     const hasCredits = user.credits && user.credits > 0;

  //     if (hasActivePlan || hasCredits) {
  //       console.log(
  //         "Authenticated user with active plan detected, redirecting to dashboard"
  //       );
  //       navigate("/dashboard");
  //     }
  //   }
  // }, [isAuthenticated, isLoading, navigate, user]);

  // Show loading while checking authentication
  if (isLoading) {
    return (
      <div
        style={{
          minHeight: "100vh",
          width: "100vw",
          backgroundColor: "#000000",
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          color: "#ffffff",
          fontSize: "18px",
        }}
      >
        Loading...
      </div>
    );
  }

  // Don't render if not authenticated (redirect is handled in useEffect)
  if (!isAuthenticated) {
    return null;
  }

  const handleSubscribe = (planName: string) => {
    // 1. Double-check that the user object is available.
    if (!user) {
      console.error("User is not available. Cannot proceed with payment.");
      // You could show a toast message here.
      return;
    }

    console.log(
      `Creating payment link for user: ${user.id} and plan: ${planName}`
    );

    let checkoutUrl = "";
    switch (planName.toLowerCase()) {
      case "basic":
        checkoutUrl = "https://buy.stripe.com/test_4gM5kF9gsfrR3ar2HvdEs00";
        break;
      case "pro":
        checkoutUrl = "https://buy.stripe.com/test_9B6bJ38co2F5dP55THdEs01";
        break;
      case "enterprise":
        checkoutUrl = "https://buy.stripe.com/test_7sY9AV1O07ZpfXdci5dEs02";
        break;
      default:
        console.error("Unknown plan:", planName);
        return;
    }

    const finalUrl = `${checkoutUrl}?client_reference_id=${
      user.id
    }&prefilled_email=${encodeURIComponent(user.email || "")}`;

    console.log("Opening secure Stripe URL:", finalUrl);

    // 3. Open the unique, user-specific URL.
    window.open(finalUrl, "_blank");
  };

  // Define all plans
  const plans: Plan[] = [
    {
      name: "Basic",
      price: 15,
      clips: "200 Video Clips",
      buttonText: "Choose Basic",
      buttonStyle: "basic",
    },
    {
      name: "Pro",
      price: 30,
      clips: "500 Video Clips",
      buttonText: "Choose Pro",
      buttonStyle: "pro",
      isPopular: true,
    },
    {
      name: "Enterprise",
      price: 50,
      clips: "Unlimited Clips",
      buttonText: "Choose Enterprise",
      buttonStyle: "enterprise",
    },
  ];

  // Styles

  const containerStyle: React.CSSProperties = {
    minHeight: "100vh",
    backgroundColor: "#000000",
    padding: "20px 16px",
    margin: "0",
    boxSizing: "border-box",
    fontFamily:
      '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif',
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
  };

  const wrapperStyle: React.CSSProperties = {
    maxWidth: "900px",
    width: "100%",
    margin: "0 auto",
  };

  const headerStyle: React.CSSProperties = {
    textAlign: "center",
    marginBottom: "32px",
  };

  const titleStyle: React.CSSProperties = {
    fontSize: "28px",
    fontWeight: "bold",
    color: "#ffffff",
    marginBottom: "16px",
    lineHeight: "1.2",
  };

  const subtitleStyle: React.CSSProperties = {
    fontSize: "16px",
    color: "#d1d5db",
    lineHeight: "1.5",
    maxWidth: "600px",
    margin: "0 auto",
  };

  const cardStyle: React.CSSProperties = {
    backgroundColor: "white",
    borderRadius: "12px",
    padding: "24px",
    boxShadow:
      "0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)",
    position: "relative",
  };

  const planHeaderStyle: React.CSSProperties = {
    textAlign: "center",
    marginBottom: "24px",
  };

  const planTitleStyle: React.CSSProperties = {
    fontSize: "20px",
    fontWeight: "600",
    color: "#111827",
    marginBottom: "8px",
  };

  const priceStyle: React.CSSProperties = {
    fontSize: "32px",
    fontWeight: "bold",
    color: "#111827",
  };

  const priceSubtextStyle: React.CSSProperties = {
    fontSize: "14px",
    color: "#6b7280",
    fontWeight: "normal",
  };

  return (
    <div style={containerStyle}>
      <div style={wrapperStyle}>
        {/* Header */}
        <div style={headerStyle}>
          <h1 style={titleStyle}>Choose Your Video Plan</h1>
          <p style={subtitleStyle}>
            Select the perfect plan for your video creation needs. Get access to
            professional tools and features.
          </p>
        </div>

        {/* Plans */}
        <div
          style={{
            display: "grid",
            gridTemplateColumns: "repeat(auto-fit, minmax(280px, 1fr))",
            gap: "20px",
            maxWidth: "900px",
            margin: "0 auto",
          }}
        >
          {plans.map((plan, index) => {
            const getButtonStyle = (buttonType: string) => {
              const baseStyle = {
                width: "100%",
                padding: "12px 16px",
                borderRadius: "8px",
                fontSize: "14px",
                fontWeight: "500",
                border: "none",
                cursor: "pointer",
                transition: "background-color 0.2s",
              };

              switch (buttonType) {
                case "basic":
                  return {
                    ...baseStyle,
                    backgroundColor: "#059669",
                    color: "white",
                  };
                case "pro":
                  return {
                    ...baseStyle,
                    backgroundColor: "#2563eb",
                    color: "white",
                  };
                case "enterprise":
                  return {
                    ...baseStyle,
                    backgroundColor: "#7c3aed",
                    color: "white",
                  };
                default:
                  return baseStyle;
              }
            };

            const cardStyleForPlan = plan.isPopular
              ? { ...cardStyle, border: "2px solid #2563eb" }
              : cardStyle;

            return (
              <div key={index} style={cardStyleForPlan}>
                {plan.isPopular && (
                  <div
                    style={{
                      position: "absolute",
                      top: "-12px",
                      left: "50%",
                      transform: "translateX(-50%)",
                      backgroundColor: "#2563eb",
                      color: "white",
                      padding: "4px 16px",
                      borderRadius: "20px",
                      fontSize: "12px",
                      fontWeight: "500",
                    }}
                  >
                    Most Popular
                  </div>
                )}

                <div
                  style={{
                    textAlign: "center",
                    marginBottom: "32px",
                  }}
                >
                  <h3 style={planTitleStyle}>{plan.name}</h3>
                  <div style={priceStyle}>
                    ${plan.price}
                    <span style={priceSubtextStyle}> per month</span>
                  </div>
                  <div
                    style={{
                      fontSize: "16px",
                      color: "#2563eb",
                      fontWeight: "600",
                      marginTop: "8px",
                    }}
                  >
                    {plan.clips}
                  </div>
                </div>

                <button
                  onClick={() => handleSubscribe(plan.name)}
                  style={getButtonStyle(plan.buttonStyle)}
                  onMouseOver={(e) => {
                    if (plan.buttonStyle === "basic") {
                      e.currentTarget.style.backgroundColor = "#047857";
                    } else if (plan.buttonStyle === "pro") {
                      e.currentTarget.style.backgroundColor = "#1d4ed8";
                    } else if (plan.buttonStyle === "enterprise") {
                      e.currentTarget.style.backgroundColor = "#6d28d9";
                    }
                  }}
                  onMouseOut={(e) => {
                    if (plan.buttonStyle === "basic") {
                      e.currentTarget.style.backgroundColor = "#059669";
                    } else if (plan.buttonStyle === "pro") {
                      e.currentTarget.style.backgroundColor = "#2563eb";
                    } else if (plan.buttonStyle === "enterprise") {
                      e.currentTarget.style.backgroundColor = "#7c3aed";
                    }
                  }}
                >
                  {plan.buttonText}
                </button>
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
};

export default PaymentPlanComponent;
