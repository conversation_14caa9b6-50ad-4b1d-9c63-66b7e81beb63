import asyncio
from openai import AsyncOpenAI
import url_processor
import storage
from advanced_video_processor import AdvancedVideoProcessor, process_video_with_enhancements
import video_processing
import models
from database import SessionLocal, engine, Base
import time
from fastapi.staticfiles import StaticFiles
import requests
from google.cloud import texttospeech
import openai
from moviepy.editor import VideoFileClip
import cloudinary.uploader
import cloudinary
from sqlalchemy.orm import Session
from pydantic import BaseModel
from passlib.context import CryptContext
from jose import JWTError, jwt
from fastapi.security import OAuth2PasswordBearer, OAuth2PasswordRequestForm
from fastapi.middleware.cors import CORSMiddleware
from fastapi import FastAPI, Depends, HTTPException, status, File, UploadFile, Form, BackgroundTasks
import json
from datetime import datetime, timedelta
from typing import List, Optional, Dict, Any
import subprocess
import logging
import shutil
import os
os.environ["TOKENIZERS_PARALLELISM"] = "false"

# Configure FFmpeg path BEFORE importing any MoviePy modules


def find_ffmpeg():
    # First try to find ffmpeg in PATH
    try:
        # for macOS/Linux - `which`, and for Windows - `where`
        cmd = 'where' if os.name == 'nt' else 'which'
        result = subprocess.run(
            [cmd, 'ffmpeg'], capture_output=True, text=True)
        if result.returncode == 0:
            ffmpeg_path = result.stdout.strip().split('\n')[0]
            return ffmpeg_path
    except:
        pass

    # Fallback common paths (only for Windows)
    if os.name == 'nt':
        common_paths = [
            r"C:\ffmpeg\bin\ffmpeg.exe",
            r"C:\ffmpeg\ffmpeg-7.1.1-essentials_build\bin\ffmpeg.exe",
            r"C:\Program Files\ffmpeg\bin\ffmpeg.exe",
            r"C:\Program Files (x86)\ffmpeg\bin\ffmpeg.exe"
        ]

        for path in common_paths:
            if os.path.exists(path):
                return path

    return None


# Set FFmpeg path as environment variable before importing MoviePy
ffmpeg_path = find_ffmpeg()
if ffmpeg_path:
    os.environ['FFMPEG_BINARY'] = ffmpeg_path
    print(f"Using FFmpeg at: {ffmpeg_path}")
else:
    print("Warning: FFmpeg not found in system PATH or common locations")

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize Google Cloud TTS Client
client = texttospeech.TextToSpeechClient()

# Initialize the app
app = FastAPI(title="QuikClips API",
              description="Backend API for QuikClips video processing")

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # For production, replace with specific origins
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

app.mount("/static", StaticFiles(directory="static"), name="static")


# Create tables
Base.metadata.create_all(bind=engine)

# Directory for temporary files
TEMP_DIR = "temp"
os.makedirs(TEMP_DIR, exist_ok=True)

# Authentication configuration
SECRET_KEY = os.getenv("SECRET_KEY", "development_secret_key")
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 60 * 24  # 24 hours

# Password hashing
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")
oauth2_scheme = OAuth2PasswordBearer(tokenUrl="token")

# Initialize Cloudinary
cloudinary.config(
    cloud_name=os.getenv("CLOUDINARY_CLOUD_NAME"),
    api_key=os.getenv("CLOUDINARY_API_KEY"),
    api_secret=os.getenv("CLOUDINARY_API_SECRET")
)

# Initialize S3 storage if configured
s3_enabled = os.getenv("AWS_ACCESS_KEY_ID") and os.getenv(
    "AWS_SECRET_ACCESS_KEY")
if s3_enabled:
    storage.initialize_s3()

# --- Authentication Models ---


class Token(BaseModel):
    access_token: str
    token_type: str
    user_id: int
    username: str
    subscription: str


class TokenData(BaseModel):
    username: Optional[str] = None


class UserCreate(BaseModel):
    username: str
    email: str
    password: str


class UserProfile(BaseModel):
    username: str
    email: str
    avatar_url: Optional[str] = None
    bio: Optional[str] = None
    subscription: str = "free"

# --- Video Processing Models ---


class VideoSegment(BaseModel):
    start_time: float
    end_time: float
    text: str


class ViralityAnalysisResult(BaseModel):
    score: int
    feedback: str


class ProcessedVideo(BaseModel):
    segments: List[VideoSegment]
    video_urls: List[str]
    # virality_result: Optional[Dict[str, str]] = None
    virality_analysis: Optional[ViralityAnalysisResult] = None


class URLProcessRequest(BaseModel):
    url: str
    min_duration: float = 10.0
    max_duration: float = 60.0
    quality: str = "best"
    max_clips: Optional[int] = 10  # Default to 10 clips
    analyze_virality: Optional[bool] = False,
    platform: Optional[str] = None


class URLValidationResponse(BaseModel):
    valid: bool
    platform: Optional[str] = None
    video_id: Optional[str] = None
    error: Optional[str] = None


class VideoMetadataResponse(BaseModel):
    title: str
    duration: float
    uploader: str
    view_count: int
    platform: str
    thumbnail: str

# Advanced Video Processing Models


class AdvancedProcessingOptions(BaseModel):
    add_subtitles: bool = True
    add_emojis: bool = True
    add_clipart: bool = True
    create_short_form: bool = True
    platforms: List[str] = ["tiktok", "instagram"]
    subtitle_style: str = "modern"  # modern, tiktok, elegant
    max_short_clips: int = 3


class AdvancedProcessingRequest(BaseModel):
    video_url: Optional[str] = None  # For URL processing
    options: AdvancedProcessingOptions = AdvancedProcessingOptions()


class AdvancedProcessingResponse(BaseModel):
    success: bool
    message: str
    original_video: str
    processed_videos: Dict[str, str] = {}
    short_form_clips: Dict[str, List[str]] = {}
    metadata: Dict[str, Any] = {}
    processing_time: float = 0.0
    error: Optional[str] = None


class ProcessedClipDetail(BaseModel):
    url: str
    text: str
    start_time: float
    end_time: float
    virality_analysis: Optional[ViralityAnalysisResult] = None,
    platform: Optional[str] = None

# --- Helper Functions ---


def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()


def verify_password(plain_password, hashed_password):
    return pwd_context.verify(plain_password, hashed_password)


def get_password_hash(password):
    return pwd_context.hash(password)


def get_user(db, username: str):
    return db.query(models.User).filter(models.User.username == username).first()


def authenticate_user(db, username: str, password: str):
    user = get_user(db, username)
    if not user or not verify_password(password, user.hashed_password):
        return False
    return user


def create_access_token(data: dict, expires_delta: Optional[timedelta] = None):
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=15)
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt


async def get_current_user(token: str = Depends(oauth2_scheme), db: Session = Depends(get_db)):
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        username: str = payload.get("sub")
        if username is None:
            raise credentials_exception
        token_data = TokenData(username=username)
    except JWTError:
        raise credentials_exception
    user = get_user(db, username=token_data.username)
    if user is None:
        raise credentials_exception
    return user

# Initialize OpenAI client
client = AsyncOpenAI()


async def analyze_virality_with_openai(transcript: str) -> Optional[Dict[str, Any]]:
    prompt = (
        f"Analyze the following video transcript for its potential virality. "
        f"You MUST respond with a valid JSON object containing two keys: "
        f"1. 'score': An integer from 0 to 100. "
        f"2. 'feedback': A string containing a few bullet points of actionable advice to improve virality. Use markdown for bullets (e.g., '* Point 1\\n* Point 2'). "
        f"Do not include any text outside of the JSON object.\n\n"
        f"Transcript to Analyze:\n{transcript[:3000]}"
    )

    try:
        response = await client.chat.completions.create(
            model="gpt-4-turbo-preview",
            messages=[
                # More explicit system prompt
                {"role": "system", "content": "You are a helpful assistant that analyzes text and responds ONLY with a valid, complete JSON object. Do not write any other text."},
                {"role": "user", "content": prompt}
            ],
            # INCREASED TOKENS to prevent cutoff
            max_tokens=400,
            temperature=0.4,
            response_format={"type": "json_object"},
        )

        result_string = response.choices[0].message.content.strip()
        logger.info(f"Raw virality analysis from OpenAI: '{result_string}'")

        try:
            parsed_data = json.loads(result_string)
            return parsed_data
        except json.JSONDecodeError:
            logger.error(f"Failed to decode JSON from OpenAI: {result_string}")
            return None

    except Exception as e:
        logger.error(f"OpenAI virality analysis API call failed: {e}")
        return None

# # --- Routes ---


@app.post("/token", response_model=Token)
async def login_for_access_token(form_data: OAuth2PasswordRequestForm = Depends(), db: Session = Depends(get_db)):
    user = authenticate_user(db, form_data.username, form_data.password)

    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect username or password",
            headers={"WWW-Authenticate": "Bearer"},
        )
    access_token_expires = timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = create_access_token(
        data={"sub": user.username}, expires_delta=access_token_expires
    )
    return {
        "access_token": access_token,
        "token_type": "bearer",
        "user_id": user.id,
        "username": user.username,
        "subscription": user.subscription
    }


@app.post("/users/", response_model=UserProfile)
async def create_user(user: UserCreate, db: Session = Depends(get_db)):
    db_user = get_user(db, username=user.username)
    if db_user:
        raise HTTPException(
            status_code=400, detail="Username already registered")

    email_exists = db.query(models.User).filter(
        models.User.email == user.email).first()
    if email_exists:
        raise HTTPException(status_code=400, detail="Email already registered")

    hashed_password = get_password_hash(user.password)
    db_user = models.User(
        username=user.username,
        email=user.email,
        hashed_password=hashed_password,
        subscription="free"
    )
    db.add(db_user)
    db.commit()
    db.refresh(db_user)

    return {
        "username": db_user.username,
        "email": db_user.email,
        "avatar_url": db_user.avatar_url,
        "bio": db_user.bio,
        "subscription": db_user.subscription
    }


@app.get("/users/me/", response_model=UserProfile)
async def read_users_me(current_user: models.User = Depends(get_current_user)):
    return {
        "username": current_user.username,
        "email": current_user.email,
        "avatar_url": current_user.avatar_url,
        "bio": current_user.bio,
        "subscription": current_user.subscription
    }


@app.post("/upload", response_model=ProcessedVideo)
async def upload_video(
    background_tasks: BackgroundTasks,
    file: UploadFile = File(...),
    storage_type: str = Form("cloudinary"),
    min_duration: float = Form(10.0),
    max_duration: float = Form(60.0),
    current_user: models.User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    # Check subscription limits
    if current_user.subscription == "free" and db.query(models.Video).filter(models.Video.user_id == current_user.id).count() >= 5:
        raise HTTPException(
            status_code=402, detail="Free subscription limit reached (5 videos)")

    try:
        # Save the uploaded video temporarily
        temp_video_path = os.path.join(TEMP_DIR, file.filename)
        with open(temp_video_path, "wb") as temp_video:
            shutil.copyfileobj(file.file, temp_video)

        # Log video info
        try:
            # clip = VideoFileClip(temp_video_path)
            with VideoFileClip(clip_path) as clip:
                duration = clip.duration
                resolution = f"{clip.size[0]}x{clip.size[1]}"
                clip.close()
        except Exception as e:
            logger.error(f"Error getting video info: {str(e)}")
            duration = 0
            resolution = "unknown"

        # Create video record
        db_video = models.Video(
            user_id=current_user.id,
            filename=file.filename,
            original_path=temp_video_path,
            duration=duration,
            resolution=resolution,
            status="processing"
        )
        db.add(db_video)
        db.commit()
        db.refresh(db_video)

        # Process the video
        transcript, timestamps = video_processing.transcribe_video(
            temp_video_path, TEMP_DIR)
        segments = video_processing.segment_transcript(
            transcript,
            timestamps,
            min_duration=min_duration,
            max_duration=max_duration,
            refine_with_ai=current_user.subscription != "free"
        )
        clipped_videos = video_processing.clip_video_from_text(
            temp_video_path, segments, TEMP_DIR)

        # Upload to appropriate storage
        video_urls = []
        for clip_path in clipped_videos:
            if storage_type == "s3" and s3_enabled:
                url = storage.upload_to_s3(clip_path)
            else:
                url = storage.upload_to_cloudinary(clip_path)
            video_urls.append(url)

            # Create clip record
            db_clip = models.VideoClip(
                video_id=db_video.id,
                url=url,
                duration=VideoFileClip(clip_path).duration,
                status="completed"
            )
            db.add(db_clip)

        # Update video status
        db_video.status = "completed"
        db_video.processed_count = len(clipped_videos)
        db.commit()

        # Clean up in background
        def cleanup():
            try:
                os.remove(temp_video_path)
                for clip in clipped_videos:
                    if os.path.exists(clip):
                        os.remove(clip)
            except Exception as e:
                logger.error(f"Cleanup error: {str(e)}")

        background_tasks.add_task(cleanup)

        return {
            "segments": [
                {"start_time": s["start"],
                    "end_time": s["end"], "text": s["text"]}
                for s in segments
            ],
            "video_urls": video_urls
        }

    except Exception as e:
        logger.error(f"Upload error: {str(e)}")
        if 'db_video' in locals():
            db_video.status = "failed"
            db_video.error_message = str(e)
            db.commit()
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/edit/trim")
async def trim_video(
    file: UploadFile = File(...),
    start_time: float = Form(...),
    end_time: float = Form(...),
    current_user: models.User = Depends(get_current_user)
):
    try:
        # Save the uploaded video temporarily
        temp_video_path = os.path.join(TEMP_DIR, file.filename)
        with open(temp_video_path, "wb") as temp_video:
            shutil.copyfileobj(file.file, temp_video)

        # Trim the video
        output_path = os.path.join(TEMP_DIR, f"trimmed_{file.filename}")
        video_processing.trim_video(
            temp_video_path, output_path, start_time, end_time)

        # Upload to Cloudinary
        url = storage.upload_to_cloudinary(output_path)

        # Cleanup
        os.remove(temp_video_path)
        os.remove(output_path)

        return {"url": url}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/edit/speed")
async def adjust_speed(
    file: UploadFile = File(...),
    speed_factor: float = Form(...),
    current_user: models.User = Depends(get_current_user)
):
    try:
        # Save the uploaded video temporarily
        temp_video_path = os.path.join(TEMP_DIR, file.filename)
        with open(temp_video_path, "wb") as temp_video:
            shutil.copyfileobj(file.file, temp_video)

        # Adjust speed
        output_path = os.path.join(TEMP_DIR, f"speed_{file.filename}")
        video_processing.adjust_speed(
            temp_video_path, output_path, speed_factor)

        # Upload to Cloudinary
        url = storage.upload_to_cloudinary(output_path)

        # Cleanup
        os.remove(temp_video_path)
        os.remove(output_path)

        return {"url": url}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/edit/crop")
async def crop_video(
    file: UploadFile = File(...),
    x1: int = Form(...),
    y1: int = Form(...),
    x2: int = Form(...),
    y2: int = Form(...),
    current_user: models.User = Depends(get_current_user)
):
    try:
        # Save the uploaded video temporarily
        temp_video_path = os.path.join(TEMP_DIR, file.filename)
        with open(temp_video_path, "wb") as temp_video:
            shutil.copyfileobj(file.file, temp_video)

        # Crop the video
        output_path = os.path.join(TEMP_DIR, f"cropped_{file.filename}")
        video_processing.crop_video(
            temp_video_path, output_path, x1, y1, x2, y2)

        # Upload to Cloudinary
        url = storage.upload_to_cloudinary(output_path)

        # Cleanup
        os.remove(temp_video_path)
        os.remove(output_path)

        return {"url": url}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/edit/rotate")
async def rotate_video(
    file: UploadFile = File(...),
    angle: int = Form(...),
    current_user: models.User = Depends(get_current_user)
):
    try:
        # Save the uploaded video temporarily
        temp_video_path = os.path.join(TEMP_DIR, file.filename)
        with open(temp_video_path, "wb") as temp_video:
            shutil.copyfileobj(file.file, temp_video)

        # Rotate the video
        output_path = os.path.join(TEMP_DIR, f"rotated_{file.filename}")
        video_processing.rotate_video(temp_video_path, output_path, angle)

        # Upload to Cloudinary
        url = storage.upload_to_cloudinary(output_path)

        # Cleanup
        os.remove(temp_video_path)
        os.remove(output_path)

        return {"url": url}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/edit/merge")
async def merge_videos(
    files: List[UploadFile] = File(...),
    current_user: models.User = Depends(get_current_user)
):
    try:
        temp_paths = []
        for file in files:
            temp_path = os.path.join(TEMP_DIR, file.filename)
            with open(temp_path, "wb") as temp_video:
                shutil.copyfileobj(file.file, temp_video)
            temp_paths.append(temp_path)

        # Merge the videos
        output_path = os.path.join(
            TEMP_DIR, f"merged_{datetime.now().timestamp()}.mp4")
        video_processing.merge_videos(temp_paths, output_path)

        # Upload to Cloudinary
        url = storage.upload_to_cloudinary(output_path)

        # Cleanup
        for path in temp_paths:
            os.remove(path)
        os.remove(output_path)

        return {"url": url}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/videos/")
async def list_videos(
    skip: int = 0,
    limit: int = 20,
    current_user: models.User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    videos = db.query(models.Video).filter(
        models.Video.user_id == current_user.id
    ).offset(skip).limit(limit).all()

    result = []
    for video in videos:
        clips = db.query(models.VideoClip).filter(
            models.VideoClip.video_id == video.id).all()
        result.append({
            "id": video.id,
            "filename": video.filename,
            "duration": video.duration,
            "created_at": video.created_at,
            "status": video.status,
            "clips": [{"id": clip.id, "url": clip.url, "duration": clip.duration} for clip in clips]
        })

    return result


@app.delete("/videos/{video_id}")
async def delete_video(
    video_id: int,
    current_user: models.User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    video = db.query(models.Video).filter(
        models.Video.id == video_id,
        models.Video.user_id == current_user.id
    ).first()

    if not video:
        raise HTTPException(status_code=404, detail="Video not found")

    # Delete associated clips
    clips = db.query(models.VideoClip).filter(
        models.VideoClip.video_id == video.id).all()
    for clip in clips:
        db.delete(clip)

    # Delete video record
    db.delete(video)
    db.commit()

    return {"detail": "Video deleted successfully"}

# --- URL Processing Endpoints ---


class URLRequest(BaseModel):
    url: str


@app.post("/validate-url", response_model=URLValidationResponse)
async def validate_video_url(request: URLRequest):
    """Validate if URL is from a supported platform - Public endpoint"""
    try:
        processor = url_processor.URLVideoProcessor()
        validation = processor.validate_url(request.url)

        return URLValidationResponse(
            valid=validation['valid'],
            platform=validation.get('platform'),
            video_id=validation.get('video_id'),
            error=validation.get('error')
        )
    except Exception as e:
        logger.error(f"URL validation error: {str(e)}")
        return URLValidationResponse(
            valid=False,
            error=str(e)
        )


@app.post("/url-metadata")
async def get_url_metadata(request: URLRequest):
    """Get video metadata from URL without downloading - Public endpoint"""
    try:
        processor = url_processor.URLVideoProcessor()
        metadata = processor.get_video_metadata(request.url)

        if 'error' in metadata:
            raise HTTPException(status_code=400, detail=metadata['error'])

        return metadata
    except Exception as e:
        logger.error(f"Metadata extraction error: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

# Optional authentication dependency


async def get_current_user_optional(token: str = Depends(oauth2_scheme), db: Session = Depends(get_db)):
    """Get current user if authenticated, otherwise return None"""
    if not token:
        return None
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        username: str = payload.get("sub")
        if username is None:
            return None
        user = get_user(db, username=username)
        return user
    except JWTError:
        return None


class URLProcessResponse(BaseModel):
    clips: List[ProcessedClipDetail]


@app.post("/process-url", response_model=URLProcessResponse)
async def process_video_url(
    request: URLProcessRequest,
    background_tasks: BackgroundTasks,
    current_user: Optional[models.User] = Depends(get_current_user_optional),
    db: Session = Depends(get_db)
):
    """Download and process video from URL - Works with or without authentication"""
    # Check subscription limits only if user is authenticated
    if current_user and current_user.subscription == "free" and db.query(models.Video).filter(models.Video.user_id == current_user.id).count() >= 5:
        raise HTTPException(
            status_code=402, detail="Free subscription limit reached (5 videos)")

    try:
        # Validate URL first
        processor = url_processor.URLVideoProcessor(TEMP_DIR)
        validation = processor.validate_url(request.url)

        if not validation['valid']:
            raise HTTPException(status_code=400, detail=validation['error'])
        request.analyze_virality = True

        # Download video
        logger.info(f"Processing URL: {request.url}")
        video_path, download_metadata = processor.download_video(
            request.url, request.quality)

        # Create video record only if user is authenticated
        db_video = None
        if current_user:
            db_video = models.Video(
                user_id=current_user.id,
                filename=f"url_{download_metadata['platform']}_{download_metadata['video_id']}.mp4",
                original_path=video_path,
                duration=download_metadata.get('duration', 0),
                resolution=download_metadata.get('resolution', 'unknown'),
                status="processing"
            )
            db.add(db_video)
            db.commit()
            db.refresh(db_video)

        # Process the video using existing pipeline
        transcript, timestamps = video_processing.transcribe_video(
            video_path, TEMP_DIR)

        segments = video_processing.segment_transcript(
            transcript,
            timestamps,
            min_duration=request.min_duration,
            max_duration=request.max_duration,
            refine_with_ai=current_user.subscription != "free" if current_user else False
        )

        if request.max_clips:
            segments = segments[:request.max_clips]

        logger.info(f"Created {len(segments)} segments from transcript.")

    # --- Step 4: Analyze each segment's text CONCURRENTLY ---
        analysis_results = []
        if request.analyze_virality and segments:
            logger.info(
                f"Analyzing virality for {len(segments)} segments in parallel...")
            # Create a list of analysis tasks, one for each segment's text
            analysis_tasks = [analyze_virality_with_openai(
                s['text']) for s in segments]
            # Run all API calls at the same time and wait for them all to complete
            analysis_results = await asyncio.gather(*analysis_tasks)
        else:
            # If not analyzing, create a list of Nones to match the number of segments
            analysis_results = [None] * len(segments)

        # --- Step 5: Create local video clips ---
        clipped_videos_paths = video_processing.clip_video_from_text(
            video_path, segments, TEMP_DIR
        )

        # --- Step 6: Loop through results, upload, and build final response object ---
        final_clips_details: List[ProcessedClipDetail] = []

        # Zip combines the corresponding items from each list into tuples
        for segment, clip_path, analysis_data in zip(segments, clipped_videos_paths, analysis_results):
            # Upload the physical clip to get its public URL
            url = storage.upload_to_cloudinary(clip_path)

            # Create a ViralityAnalysisResult object from the dictionary if it exists
            virality_obj = ViralityAnalysisResult(
                **analysis_data) if analysis_data else None

            # Combine all information for this one clip
            final_clips_details.append(
                ProcessedClipDetail(
                    url=url,
                    text=segment['text'],
                    start_time=segment['start'],
                    end_time=segment['end'],
                    virality_analysis=virality_obj,
                    platform=validation.get('platform', 'unknown')
                )
            )

        # --- Step 7: Cleanup (No change) ---
        def cleanup():
            try:
                processor.cleanup_downloaded_file(video_path)
                for clip_path in clipped_videos_paths:
                    if os.path.exists(clip_path):
                        os.remove(clip_path)
            except Exception as e:
                logger.error(f"Cleanup error: {str(e)}")
        background_tasks.add_task(cleanup)

        print(f"Final clips details: {final_clips_details}")
        logging.info(f"Final clips details: {final_clips_details}")

        # --- Step 8: Return the final, structured response ---
        return URLProcessResponse(clips=final_clips_details)

    except Exception as e:
        logger.error(f"URL processing error: {e}", exc_info=True)
        # Your DB error handling can go here
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/advanced-process", response_model=AdvancedProcessingResponse)
async def advanced_video_processing(
    request: str = Form(...),
    file: UploadFile | None = File(default=None),
    background_tasks: BackgroundTasks = BackgroundTasks(),
    current_user: Optional[models.User] = Depends(get_current_user_optional),
    db: Session = Depends(get_db)
):
    """
    Advanced video processing with subtitles, emojis, clipart, and short-form content creation
    Supports both file upload and URL processing
    """
    try:
        request_data = json.loads(request)
        request_model = AdvancedProcessingRequest(**request_data)

        # Check subscription limits for authenticated users
        if current_user and current_user.subscription == "free":
            video_count = db.query(models.Video).filter(
                models.Video.user_id == current_user.id).count()
            if video_count >= 5:
                raise HTTPException(
                    status_code=402, detail="Free subscription limit reached (5 videos)")

        # Determine input source
        video_path = None
        temp_video_path = None

        if file:
            # Handle file upload
            temp_video_path = os.path.join(
                TEMP_DIR, f"advanced_{file.filename}")
            with open(temp_video_path, "wb") as temp_video:
                shutil.copyfileobj(file.file, temp_video)
            video_path = temp_video_path

        elif request_model.video_url:
            # Handle URL processing
            processor = url_processor.URLVideoProcessor()
            validation = processor.validate_url(request_model.video_url)

            if not validation['valid']:
                raise HTTPException(
                    status_code=400, detail=f"Invalid URL: {validation.get('error', 'Unknown error')}")

            # Download video
            video_path, download_meta = processor.download_video(
                request_model.video_url, quality="best")
            if 'error' in download_meta:
                raise HTTPException(
                    status_code=400, detail=download_meta['error'])

            # video_path = download_result['local_path']
            temp_video_path = video_path
        else:
            raise HTTPException(
                status_code=400, detail="Either file upload or video_url must be provided")

        # Create output directory
        output_dir = os.path.join(
            TEMP_DIR, f"advanced_output_{datetime.now().strftime('%Y%m%d_%H%M%S')}")
        os.makedirs(output_dir, exist_ok=True)

        # Initialize advanced processor
        processor = AdvancedVideoProcessor(
            openai_api_key=os.getenv("OPENAI_API_KEY"),
            temp_dir=TEMP_DIR
        )

        # Process video with all enhancements
        processing_options = {
            'add_subtitles': request_model.options.add_subtitles,
            'add_emojis': request_model.options.add_emojis,
            'add_clipart': request_model.options.add_clipart,
            'create_short_form': request_model.options.create_short_form,
            'platforms': request_model.options.platforms,
            'subtitle_style': request_model.options.subtitle_style,
            'max_short_clips': request_model.options.max_short_clips
        }

        results = processor.process_video_comprehensive(
            video_path, output_dir, processing_options)

        # Upload processed videos to Cloudinary
        uploaded_videos = {}
        uploaded_short_clips = {}

        # Upload main processed videos
        for video_type, video_path_result in results.get('processed_videos', {}).items():
            if os.path.exists(video_path_result):
                url = storage.upload_to_cloudinary(video_path_result)
                uploaded_videos[video_type] = url

        # Upload short-form clips
        for platform, clips in results.get('short_form_clips', {}).items():
            uploaded_clips = []
            for clip_path in clips:
                if os.path.exists(clip_path):
                    url = storage.upload_to_cloudinary(clip_path)
                    uploaded_clips.append(url)
            uploaded_short_clips[platform] = uploaded_clips

        # Save to database if user is authenticated
        if current_user:
            db_video = models.Video(
                user_id=current_user.id,
                filename=file.filename if file else "url_video",
                original_path=request_model.video_url,
                status="completed",
                processed_count=len(uploaded_videos) + sum(len(clips)
                                                           for clips in uploaded_short_clips.values())
            )
            db.add(db_video)
            db.commit()
            db.refresh(db_video)

            # Save clips to database
            for video_type, url in uploaded_videos.items():
                db_clip = models.VideoClip(
                    video_id=db_video.id,
                    url=url,
                    duration=0,  # Would need to calculate actual duration
                    status="completed"
                )
                db.add(db_clip)

            for platform, clips in uploaded_short_clips.items():
                for url in clips:
                    db_clip = models.VideoClip(
                        video_id=db_video.id,
                        url=url,
                        duration=0,  # Would need to calculate actual duration
                        status="completed"
                    )
                    db.add(db_clip)

            db.commit()

        # Schedule cleanup
        def cleanup():
            try:
                if temp_video_path and os.path.exists(temp_video_path):
                    os.remove(temp_video_path)
                if os.path.exists(output_dir):
                    shutil.rmtree(output_dir)
            except Exception as e:
                logger.error(f"Cleanup error: {str(e)}")

        background_tasks.add_task(cleanup)

        return AdvancedProcessingResponse(
            success=True,
            message="Video processed successfully with advanced features",
            original_video=video_path,
            processed_videos=uploaded_videos,
            short_form_clips=uploaded_short_clips,
            metadata=results.get('metadata', {}),
            processing_time=results.get('processing_time', 0.0)
        )

    except Exception as e:
        logger.error(f"Advanced processing error: {str(e)}")
        return AdvancedProcessingResponse(
            success=False,
            message="Processing failed",
            original_video="",
            error=str(e)
        )


class GenerationRequest(BaseModel):
    prompt: str
    image: str
    platform: str
    duration: int  # Duration of the video in seconds


class GenerationResponse(BaseModel):
    script: str


# Set your OpenAI API Key
openai_client = openai.OpenAI(api_key=os.getenv("OPENAI_API_KEY"))


os.environ['GOOGLE_APPLICATION_CREDENTIALS'] = 'angular-argon-452914-f1-17bffe088833.json'


@app.post("/generate", response_model=GenerationResponse)
async def generate_video_script(request: GenerationRequest):
    try:

        detailed_prompt = (
            f"Create a highly detailed video script for {request.platform} based on the following idea:\n\n"
            f"Idea: {request.prompt}\n"
            f"Target Duration: {request.duration} seconds.\n\n"
            "The script must be clearly structured, alternating between SCENE descriptions and NARRATOR speeches.\n"
            "Each SCENE should be labeled like 'SCENE 1:', 'SCENE 2:', etc., and be extremely vivid and visual and look like this {request.image}.\n"
            "Each NARRATOR line should be emotional, storytelling-style, and connected to the scene.\n\n"
            "FORMAT STRICTLY LIKE THIS:\n\n"
            "SCENE 1:\n"
            "[Detailed description of the first visual scene — colors, environment, emotions, action.]\n\n"
            "NARRATOR:\n"
            "[Narration for the first scene — short, impactful, and expressive.]\n\n"
            "SCENE 2:\n"
            "[Detailed description of the second visual scene.]\n\n"
            "NARRATOR:\n"
            "[Narration for the second scene.]\n\n"
            "Create at least 3 to 5 SCENE and NARRATOR pairs."
        )

        # New way of calling Chat Completions
        response = openai_client.chat.completions.create(
            model="gpt-4",
            messages=[
                {"role": "system", "content": "You are a creative video scriptwriter."},
                {"role": "user", "content": detailed_prompt}
            ],
            temperature=0.7,
            max_tokens=800
        )

        script = response.choices[0].message.content

        return GenerationResponse(script=script)

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


# Define the request model for generating audio
class AudioGenerationRequest(BaseModel):
    script_text: str
    # Default to English if not provided
    language_code: Optional[str] = "en-US"
    voice_name: Optional[str] = "en-US-Wavenet-D"  # Default voice option

# Define the response model


class AudioGenerationResponse(BaseModel):
    audio_url: str  # The URL or file path of the generated audio


# Request model for audio and image
class AudioRequest(BaseModel):
    script_text: str
    session_name: str
    scene_number: int
    voice_type: str
    platform: str


class ImageRequest(BaseModel):
    script: dict
    session_name: str
    scene_number: int
    mediaType: str
    platform: str


# Helper function to create the session folder and subfolder structure
def create_session_folder(session_name):
    base_folder = f"static/sessions/{session_name}"

    # Check if the base folder exists, if not create it
    if not os.path.exists(base_folder):
        os.makedirs(base_folder, exist_ok=True)

    # Subfolders for images and audio inside the session folder
    image_folder = os.path.join(base_folder, "images")
    audio_folder = os.path.join(base_folder, "audio")

    # Create subfolders if they don't exist
    os.makedirs(image_folder, exist_ok=True)
    os.makedirs(audio_folder, exist_ok=True)

    return base_folder, image_folder, audio_folder


# ElevenLabs config
ELEVENLABS_API_KEY = os.getenv("ELEVENLABS_API_KEY")  # set this securely
VOICE_ID = "EXAVITQu4vr4xnSDxMaL"  # Example voice ID (you can change this)


def create_session_folder(session_name):
    base_folder = f"static/sessions/{session_name}"
    image_folder = os.path.join(base_folder, "images")
    audio_folder = os.path.join(base_folder, "audio")

    os.makedirs(image_folder, exist_ok=True)
    os.makedirs(audio_folder, exist_ok=True)

    return base_folder, image_folder, audio_folder


@app.post("/generate-audio")
async def generate_audio(request: AudioRequest):
    try:
        session_name = request.session_name
        scene_number = request.scene_number
        script_text = request.script_text
        voice_type = request.voice_type

        print(voice_type)

        # Create folders
        base_folder, image_folder, audio_folder = create_session_folder(
            session_name)

        # ElevenLabs API call
        url = f"https://api.elevenlabs.io/v1/text-to-speech/{voice_type}"

        headers = {
            "xi-api-key": ELEVENLABS_API_KEY,
            "Content-Type": "application/json"
        }

        payload = {
            "text": script_text,
            "model_id": "eleven_monolingual_v1",
            "voice_settings": {
                "stability": 0.5,
                "similarity_boost": 0.75
            }
        }

        response = requests.post(url, headers=headers, json=payload)

        if response.status_code != 200:
            raise HTTPException(
                status_code=500, detail=f"ElevenLabs error: {response.text}")

        # Save audio file
        audio_filename = os.path.join(
            audio_folder, f"audio_{scene_number}.mp3")
        with open(audio_filename, "wb") as f:
            f.write(response.content)

        print(f"Audio content written to: {audio_filename}")
        return {"audio_path": audio_filename}

    except Exception as e:
        print(f"Error: {e}")
        raise HTTPException(
            status_code=500, detail=f"Error generating audio: {str(e)}")

# Audio request


@app.post("/generate-google-audio")
async def generate_audio(request: AudioRequest):
    try:
        session_name = request.session_name
        scene_number = request.scene_number
        script_text = request.script_text

        # Create session folder and subfolders
        base_folder, image_folder, audio_folder = create_session_folder(
            session_name)

        # Google Text to Speech setup
        client = texttospeech.TextToSpeechClient()

        synthesis_input = texttospeech.SynthesisInput(text=script_text)
        voice = texttospeech.VoiceSelectionParams(
            language_code="en-US",
            ssml_gender=texttospeech.SsmlVoiceGender.NEUTRAL
        )
        audio_config = texttospeech.AudioConfig(
            audio_encoding=texttospeech.AudioEncoding.MP3
        )

        # Perform speech synthesis
        response = client.synthesize_speech(
            input=synthesis_input,
            voice=voice,
            audio_config=audio_config
        )

        # Ensure we get binary data and write it to a file
        audio_filename = os.path.join(
            audio_folder, f"audio_{scene_number}.mp3")

        # Write the binary audio content to a file
        with open(audio_filename, "wb") as out:
            # Ensure response.audio_content is a binary string
            out.write(response.audio_content)

        print(f"Audio content written to: {audio_filename}")

        # Return the path to the audio file
        return {"audio_path": audio_filename}

    except Exception as e:
        print(f"Error: {e}")
        raise HTTPException(
            status_code=500, detail=f"Error generating audio: {str(e)}")


@app.post("/generate-image")
async def generate_images(request: ImageRequest):
    try:
        session_name = request.session_name
        scene_number = request.scene_number
        prompt = request.script["script"]

        # Reuse the same session folder and subfolders for images
        base_folder, image_folder, audio_folder = create_session_folder(
            session_name)

        openai_api_key = os.getenv("OPENAI_API_KEY")
        if not openai_api_key:
            raise HTTPException(
                status_code=500, detail="Missing OpenAI API key")

        response = requests.post(
            "https://api.openai.com/v1/images/generations",
            headers={"Authorization": f"Bearer {openai_api_key}"},
            json={"prompt": prompt, "n": 1, "size": "1024x1024"}
        )

        if response.status_code != 200:
            raise HTTPException(
                status_code=500, detail=f"Failed to generate images: {response.text}")

        data = response.json()
        image_url = data["data"][0]["url"]

        # Save image in the images subfolder with a unique name per scene
        image_filename = os.path.join(
            image_folder, f"image_{scene_number}.jpg")
        img_data = requests.get(image_url).content
        with open(image_filename, "wb") as img_file:
            img_file.write(img_data)

        return {"image_path": image_filename}

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


def create_video_from_audio_and_image(audio_file, image_file, output_video, platform):
    try:
        # Define platform resolutions
        platform_resolutions = {
            "youtube": "1920x1080",  # YouTube 16:9
            "tiktok": "1080x1920",  # TikTok portrait
            "instagram": "1024x1024",  # Instagram square
            "linkedin": "1200x627",  # LinkedIn
            "twitter": "1200x675",  # Twitter
        }

        # Default resolution is 1024x1024
        resolution = platform_resolutions.get(platform.lower(), "1024x1024")

        # Get width and height from the resolution
        width, height = map(int, resolution.split('x'))

        # FFmpeg command to create a video with scaling and cropping
        cmd = [
            "ffmpeg",
            "-loop", "1",  # Loop image to match audio duration
            "-framerate", "1",  # 1 frame per second
            "-t", str(get_audio_duration(audio_file)),  # Duration of audio
            "-i", image_file,  # Input image file
            "-i", audio_file,  # Input audio file
            # Scale and crop image
            "-vf", f"scale={width}x{height}:force_original_aspect_ratio=increase,crop={width}:{height}",
            "-c:v", "libx264",  # Video codec
            "-preset", "fast",  # Encoding speed preset
            "-c:a", "aac",  # Audio codec
            "-strict", "experimental",  # Experimental codecs
            "-shortest",  # Ensure video duration matches audio
            output_video  # Output video file
        ]

        # Run FFmpeg command
        subprocess.run(cmd, check=True)
        print(f"Video created: {output_video}")

    except subprocess.CalledProcessError as e:
        print(f"Error creating video: {e}")
        raise


def get_audio_duration(audio_file):
    """
    Gets the duration of the audio file using FFmpeg.
    """
    cmd = [
        "ffmpeg",
        "-i", audio_file,  # Input audio file
        "-f", "null",  # Discard output
        "-"
    ]
    result = subprocess.run(cmd, stderr=subprocess.PIPE, text=True)
    duration_line = next(
        line for line in result.stderr.splitlines() if "Duration" in line)
    duration_str = duration_line.split("Duration:")[1].split(",")[0].strip()
    h, m, s = map(float, duration_str.split(":"))
    return h * 3600 + m * 60 + s


class VideoRequest(BaseModel):
    session_name: str  # The session name for the video generation
    audio_files: List[str]  # List of audio file paths
    image_files: List[str]  # List of image file paths
    platform: str


@app.post("/generate-video")
async def generate_video(request: VideoRequest):
    try:
        session_name = request.session_name
        audio_files = request.audio_files
        image_files = request.image_files
        platform = request.platform

        if len(audio_files) != len(image_files):
            raise HTTPException(
                status_code=400, detail="Audio files and image files must have the same length.")

        video_files = []
        for i in range(len(audio_files)):
            audio_file = audio_files[i]
            image_file = image_files[i]
            output_video = os.path.join(
                f"static/sessions/{session_name}", f"scene_{i+1}.mp4")

            # Create video from audio and image with platform aspect ratio
            create_video_from_audio_and_image(
                audio_file, image_file, output_video, platform)
            video_files.append(output_video)

        final_video = os.path.join(
            f"static/sessions/{session_name}", "final_video.mp4")
        concatenate_videos(video_files, final_video)

        return {"final_video_path": final_video}

    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Error generating video: {str(e)}")


def concatenate_videos(video_files, final_output):
    """
    Concatenates multiple video files into one final video.
    """
    try:
        # Create a text file with all video file paths
        with open("video_list.txt", "w") as f:
            for video_file in video_files:
                f.write(f"file '{video_file}'\n")

        # FFmpeg command to concatenate videos
        cmd = ["ffmpeg", "-f", "concat", "-safe", "0", "-i",
               "video_list.txt", "-c", "copy", final_output]
        subprocess.run(cmd, check=True)

        print(f"Final video created: {final_output}")

        # Clean up the temporary video list file
        os.remove("video_list.txt")

    except subprocess.CalledProcessError as e:
        print(f"Error concatenating videos: {e}")
        raise


@app.get("/get-el-voices")
def get_el_voices():
    try:
        url = "https://api.elevenlabs.io/v1/voices"
        headers = {
            "xi-api-key": ELEVENLABS_API_KEY
        }

        response = requests.get(url, headers=headers)

        if response.status_code == 200:
            voices = response.json().get("voices", [])
            return {"voices": voices}
        else:
            raise HTTPException(
                status_code=response.status_code, detail=response.text)

    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Error retrieving voices: {str(e)}")


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
